import express from 'express';
import { body } from 'express-validator';
import {
  createOrder,
  getOrderById,
  updateOrderToPaid,
  getMyOrders,
  getOrders,
  updateOrderToDelivered
} from '../controllers/orderController';
import { protect, authorize } from '../middleware/auth';

const router = express.Router();

// Validation rules
const orderValidation = [
  body('orderItems')
    .isArray({ min: 1 })
    .withMessage('Order items are required'),
  body('orderItems.*.product')
    .notEmpty()
    .withMessage('Product ID is required for each item'),
  body('orderItems.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('orderItems.*.price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('shippingAddress.street')
    .trim()
    .notEmpty()
    .withMessage('Street address is required'),
  body('shippingAddress.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('shippingAddress.state')
    .trim()
    .notEmpty()
    .withMessage('State is required'),
  body('shippingAddress.zipCode')
    .trim()
    .notEmpty()
    .withMessage('Zip code is required'),
  body('shippingAddress.country')
    .trim()
    .notEmpty()
    .withMessage('Country is required'),
  body('paymentMethod')
    .isIn(['stripe', 'paypal', 'cash_on_delivery'])
    .withMessage('Invalid payment method'),
  body('itemsPrice')
    .isFloat({ min: 0 })
    .withMessage('Items price must be a positive number'),
  body('taxPrice')
    .isFloat({ min: 0 })
    .withMessage('Tax price must be a positive number'),
  body('shippingPrice')
    .isFloat({ min: 0 })
    .withMessage('Shipping price must be a positive number'),
  body('totalPrice')
    .isFloat({ min: 0 })
    .withMessage('Total price must be a positive number')
];

// Protected routes
router.post('/', protect, orderValidation, createOrder);
router.get('/myorders', protect, getMyOrders);
router.get('/:id', protect, getOrderById);
router.put('/:id/pay', protect, updateOrderToPaid);

// Admin routes
router.get('/', protect, authorize('admin'), getOrders);
router.put('/:id/deliver', protect, authorize('admin'), updateOrderToDelivered);

export default router;
