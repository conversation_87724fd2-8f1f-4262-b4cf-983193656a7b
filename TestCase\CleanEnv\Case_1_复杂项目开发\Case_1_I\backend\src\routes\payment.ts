import express from 'express';
import {
  createPaymentIntent,
  confirmPayment,
  handleWebhook,
  getStripeConfig
} from '../controllers/paymentController';
import { protect } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/config', getStripeConfig);
router.post('/webhook', express.raw({ type: 'application/json' }), handleWebhook);

// Protected routes
router.post('/create-intent', protect, createPaymentIntent);
router.post('/confirm', protect, confirmPayment);

export default router;
