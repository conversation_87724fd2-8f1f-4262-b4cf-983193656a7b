{"name": "ecommerce-platform", "version": "1.0.0", "description": "Full-stack e-commerce platform with user management, order system, and payment integration", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["ecommerce", "react", "nodejs", "typescript", "mongodb"], "author": "Developer", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}