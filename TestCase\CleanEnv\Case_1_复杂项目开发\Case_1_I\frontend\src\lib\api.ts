import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: 'user' | 'admin';
  addresses?: Address[];
}

export interface Address {
  _id?: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
}

export interface Product {
  _id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  brand: string;
  images: string[];
  inventory: number;
  sku: string;
  isActive: boolean;
  specifications: Record<string, any>;
  ratings: {
    average: number;
    count: number;
  };
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface Order {
  _id: string;
  user: string;
  orderItems: OrderItem[];
  shippingAddress: Address;
  paymentMethod: string;
  paymentResult?: {
    id: string;
    status: string;
    update_time: string;
    email_address: string;
  };
  itemsPrice: number;
  taxPrice: number;
  shippingPrice: number;
  totalPrice: number;
  isPaid: boolean;
  paidAt?: string;
  isDelivered: boolean;
  deliveredAt?: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  product: string;
  quantity: number;
  price: number;
  name: string;
  image: string;
}

// API functions
export const authAPI = {
  register: (data: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }) => api.post('/auth/register', data),
  
  login: (data: { email: string; password: string }) =>
    api.post('/auth/login', data),
  
  getMe: () => api.get('/auth/me'),
  
  updateProfile: (data: {
    firstName: string;
    lastName: string;
    phone?: string;
  }) => api.put('/auth/profile', data),
};

export const productAPI = {
  getProducts: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    brand?: string;
    minPrice?: number;
    maxPrice?: number;
    sort?: string;
  }) => api.get('/products', { params }),
  
  getProduct: (id: string) => api.get(`/products/${id}`),
  
  getCategories: () => api.get('/products/categories'),
  
  createProduct: (data: Partial<Product>) => api.post('/products', data),
  
  updateProduct: (id: string, data: Partial<Product>) =>
    api.put(`/products/${id}`, data),
  
  deleteProduct: (id: string) => api.delete(`/products/${id}`),
};

export const orderAPI = {
  createOrder: (data: {
    orderItems: OrderItem[];
    shippingAddress: Address;
    paymentMethod: string;
    itemsPrice: number;
    taxPrice: number;
    shippingPrice: number;
    totalPrice: number;
  }) => api.post('/orders', data),
  
  getOrder: (id: string) => api.get(`/orders/${id}`),
  
  getMyOrders: (params?: { page?: number; limit?: number }) =>
    api.get('/orders/myorders', { params }),
  
  updateOrderToPaid: (id: string, paymentResult: any) =>
    api.put(`/orders/${id}/pay`, paymentResult),
  
  getOrders: (params?: { page?: number; limit?: number }) =>
    api.get('/orders', { params }),
  
  updateOrderToDelivered: (id: string, trackingNumber?: string) =>
    api.put(`/orders/${id}/deliver`, { trackingNumber }),
};

export const paymentAPI = {
  createPaymentIntent: (orderId: string) =>
    api.post('/payment/create-intent', { orderId }),
  
  confirmPayment: (paymentIntentId: string) =>
    api.post('/payment/confirm', { paymentIntentId }),
  
  getStripeConfig: () => api.get('/payment/config'),
};
